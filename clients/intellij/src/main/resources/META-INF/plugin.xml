<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
        <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
        <id>com.tabbyml.intellij-filin</id>

        <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
        <name><PERSON>lin</name>

        <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
        <vendor url="https://tabbyml.com">IT-One</vendor>

        <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
        <description><![CDATA[
        <h1 id="tabby-plugin-for-intellij-platform">Filin Plugin for IntelliJ Platform</h1>
        <p><PERSON><PERSON> is an AI coding assistant that can suggest multi-line code or full functions in real-time.</p>
        <br/>
        <p>For more information, please check out our <a href="https://tabby-docs.codefine.io/">Website</a> and <a href="https://github.com/TabbyML/tabby">GitHub</a>.
        If you encounter any problem or have any suggestion, please <a href="https://github.com/TabbyML/tabby/issues/new">open an issue</a></p>
        <h2 id="requirements">Requirements</h2>
        Filin plugin requires <a href="https://nodejs.org/">Node.js</a> v18+ installed. </p>
  ]]>        </description>

        <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
        <depends>com.intellij.modules.platform</depends>
        <depends optional="true" config-file="plugin-Git4Idea.xml">Git4Idea</depends>
        <depends optional="true" config-file="plugin-kotlin.xml">org.jetbrains.kotlin</depends>

        <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
        <extensionPoints>
                <extensionPoint name="languageSupportProvider" interface="com.tabbyml.intellijtabby.languageSupport.LanguageSupportProvider" dynamic="true"/>
        </extensionPoints>

    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity implementation="com.tabbyml.intellijtabby.events.StartupActivity"/>
        <postStartupActivity implementation="com.tabbyml.intellijtabby.inlineChat.SelectionGutterIconManager" />
        <editorFactoryListener implementation="com.tabbyml.intellijtabby.events.EditorFactoryListener"/>
        <statusBarWidgetFactory id="com.tabbyml.intellijtabby.status.StatusBarWidgetFactory"
                                implementation="com.tabbyml.intellijtabby.widgets.StatusBarWidgetFactory"/>
        <actionPromoter order="last" implementation="com.tabbyml.intellijtabby.actionPromoter.EditorActionPromoter"/>
        <projectConfigurable
                parentId="editor"
                instance="com.tabbyml.intellijtabby.settings.Configurable"
                id="com.tabbyml.intellijtabby.settings.Configurable"
                displayName="Filin"/>
        <notificationGroup id="com.tabbyml.intellijtabby.notifications.info"
                           displayType="BALLOON"
                           bundle="strings"
                           key="filin.info"/>
        <notificationGroup id="com.tabbyml.intellijtabby.notifications.warning"
                           displayType="STICKY_BALLOON"
                           bundle="strings"
                           key="filin.warning"/>
        <projectService serviceInterface="com.tabbyml.intellijtabby.git.GitProvider"
                        serviceImplementation="com.tabbyml.intellijtabby.git.DummyGitProvider"/>
        <toolWindow
                id="Filin"
                anchor="right"
                icon="com.tabbyml.intellijtabby.Icons.Chat"
                factoryClass="com.tabbyml.intellijtabby.widgets.ChatToolWindowFactory"/>
        <intentionAction>
            <className>com.tabbyml.intellijtabby.inlineChat.InlineChatIntentionAction</className>
        </intentionAction>
        <codeInsight.codeVisionProvider implementation="com.tabbyml.intellijtabby.inlineChat.InlineChatAcceptCodeVisionProvider" />
        <codeInsight.codeVisionProvider implementation="com.tabbyml.intellijtabby.inlineChat.InlineChatDiscardCodeVisionProvider" />
        <highlightingPassFactory implementation="com.tabbyml.intellijtabby.inlineChat.DiffHighlighterRegister" />
    </extensions>

        <actions>
                <group id="Filin.ToolsMenu" popup="true" text="Filin" description="Filin AI code assistant">
                        <add-to-group group-id="CodeMenu" anchor="after" relative-to-action="CodeCompletionGroup"/>
                        <action id="Filin.InlineCompletion.Trigger" class="com.tabbyml.intellijtabby.actions.inlineCompletion.Trigger" text="Show Inline Completion" description="Show inline completion suggestions at the caret's position.">
                                <keyboard-shortcut first-keystroke="ctrl BACK_SLASH" keymap="$default"/>
                                <keyboard-shortcut first-keystroke="alt BACK_SLASH" keymap="$default"/>
                        </action>
                        <separator/>
                        <group id="Filin.InlineCompletionContextMenu">
                                <action id="Filin.InlineCompletion.Accept" class="com.tabbyml.intellijtabby.actions.inlineCompletion.Accept" text="Accept Completion" description="Accept the shown suggestions and insert the text.">
                                </action>
                                <action id="Filin.InlineCompletion.AcceptNextLine" class="com.tabbyml.intellijtabby.actions.inlineCompletion.AcceptNextLine" text="Accept Next Line" description="Accept the next line of shown suggestions.">
                                        <keyboard-shortcut first-keystroke="ctrl TAB" keymap="$default"/>
                                </action>
                                <action id="Filin.InlineCompletion.AcceptNextWord" class="com.tabbyml.intellijtabby.actions.inlineCompletion.AcceptNextWord" text="Accept Next Word" description="Accept the next word of shown suggestions.">
                                        <keyboard-shortcut first-keystroke="ctrl RIGHT" keymap="$default"/>
                                </action>
                                <action id="Filin.InlineCompletion.CycleNext" class="com.tabbyml.intellijtabby.actions.inlineCompletion.CycleNext" text="Cycle Next" description="Cycle to next suggestion.">
                                        <keyboard-shortcut first-keystroke="alt OPEN_BRACKET" keymap="$default"/>
                                </action>
                                <action id="Filin.InlineCompletion.CyclePrevious" class="com.tabbyml.intellijtabby.actions.inlineCompletion.CyclePrevious" text="Cycle Previous" description="Cycle to previous suggestion.">
                                        <keyboard-shortcut first-keystroke="alt CLOSE_BRACKET" keymap="$default"/>
                                </action>
                                <action id="Filin.InlineCompletion.Dismiss" class="com.tabbyml.intellijtabby.actions.inlineCompletion.Dismiss" text="Dismiss Completion" description="Hide the shown suggestions.">
                                        <keyboard-shortcut first-keystroke="ESCAPE" keymap="$default"/>
                                </action>
                        </group>
                        <separator/>
                        <action id="Filin.OpenChatToolWindow" class="com.tabbyml.intellijtabby.actions.chat.OpenChatToolWindow" text="Open Filin Chat..." description="Open a tool window to chat with Filin.">
                        </action>
                        <separator/>
                        <action id="Filin.ToggleInlineCompletionTriggerMode" class="com.tabbyml.intellijtabby.actions.ToggleInlineCompletionTriggerMode" text="Toggle Auto Inline Completion" description="Enable or disable the automatic inline completion.">
                        </action>
                        <action id="Filin.OpenSettings" class="com.tabbyml.intellijtabby.actions.OpenSettings" text="Open Settings..." description="Show settings for Filin.">
                        </action>
                        <group id="Filin.OpenOnlineHelp" popup="true" text="Open Online Help" description="View useful links for online help.">
                                <action id="Filin.OpenOnlineHelp.OpenOnlineDocumentation" class="com.tabbyml.intellijtabby.actions.OpenOnlineDocumentation" text="Online Documentation..." description="Open the documentation page in your web browser.">
                                </action>
                                <action id="Filin.OpenOnlineHelp.OpenModelRegistry" class="com.tabbyml.intellijtabby.actions.OpenModelRegistry" text="Model Registry..." description="Explore more recommend models from Filin's model registry.">
                                </action>
                                <!-- <action id="Filin.OpenOnlineHelp.JoinTabbySlackCommunity" class="com.tabbyml.intellijtabby.actions.JoinTabbySlackCommunity" text="Filin Slack Community..." description="Join Filin's Slack community to get help or feed back.">
                                </action> -->
                                <action id="Filin.OpenOnlineHelp.OpenTabbyGithubRepo" class="com.tabbyml.intellijtabby.actions.OpenTabbyGithubRepo" text="Filin GitHub Repository..." description="View the source code for Filin, and open issues.">
                                </action>
                        </group>
                        <separator/>
                        <action id="Filin.CheckIssueDetail" class="com.tabbyml.intellijtabby.actions.CheckIssueDetail" text="Check Issue Detail..." description="Show detail information for current issue.">
                        </action>
                </group>

        <group id="Filin.EditorContext" popup="true" text="Filin">
            <add-to-group group-id="EditorPopupMenu" anchor="last"/>
            <action id="Filin.InlineChat.Open" class="com.tabbyml.intellijtabby.inlineChat.InlineChatAction"
                    text="Open Filin Inline Edit"
                    description="Open a inline chat widget to chat with Filin to modify your code.">
                <keyboard-shortcut first-keystroke="ctrl I" keymap="$default"/>
            </action>
            <separator/>
            <action id="Filin.Chat.AddSelectionToChat"
                    class="com.tabbyml.intellijtabby.actions.chat.AddSelectionToChat"
                    text="Add Selection to Chat"
                    description="Add the selected text to Filin chat panel for reference.">
            </action>
            <action id="Filin.Chat.AddFileToChat"
                    class="com.tabbyml.intellijtabby.actions.chat.AddFileToChat"
                    text="Add File to Chat"
                    description="Add the current file to Filin chat panel for reference.">
            </action>
            <separator/>
            <action id="Filin.Chat.Explain"
                    class="com.tabbyml.intellijtabby.actions.chat.Explain"
                    text="Explain"
                    description="Explain the selected code using Filin chat.">
            </action>
            <action id="Filin.Chat.Fix"
                    class="com.tabbyml.intellijtabby.actions.chat.Fix"
                    text="Fix"
                    description="Fix the selected code using Filin chat.">
            </action>
            <action id="Filin.Chat.CodeReview"
                    class="com.tabbyml.intellijtabby.actions.chat.CodeReview"
                    text="Code Review"
                    description="Review the selected code using Filin chat.">
            </action>
            <action id="Filin.Chat.GenerateDocs"
                    class="com.tabbyml.intellijtabby.actions.chat.GenerateDocs"
                    text="Generate Docs"
                    description="Generate documentation for the selected code using Filin chat.">
            </action>
            <action id="Filin.Chat.GenerateTests"
                    class="com.tabbyml.intellijtabby.actions.chat.GenerateTests"
                    text="Generate Tests"
                    description="Generate tests for the selected code using Filin chat.">
            </action>
        </group>

        <group id="Filin.StatusBarPopupMenu">
                <reference id="Filin.CheckIssueDetail"/>
                <separator/>
                <reference id="Filin.ToggleInlineCompletionTriggerMode"/>
                <separator/>
                <reference id="Filin.OpenChatToolWindow"/>
                <separator/>
                <reference id="Filin.OpenSettings"/>
                <reference id="Filin.OpenOnlineHelp"/>
        </group>

        <group id="Filin.ChatToolWindowToolbar">
            <action id="Filin.Chat.ShowNewChat"
                    class="com.tabbyml.intellijtabby.actions.chat.ShowNewChat"
                    text="New"
                    icon="AllIcons.General.Add"
                    description="Start a new chat.">
            </action>
            <action id="Filin.Chat.ShowChatHistory"
                    class="com.tabbyml.intellijtabby.actions.chat.ShowChatHistory"
                    text="History"
                    icon="AllIcons.Vcs.History"
                    description="Show chat history.">
            </action>
        </group>

        <action id="Filin.InlineCompletion.TabAccept"
                class="com.tabbyml.intellijtabby.actions.inlineCompletion.TabAccept"
                text="Accept Completion (Tab)"
                description="Accept the shown suggestions if the insert text does not start with indentation.">
            <keyboard-shortcut first-keystroke="TAB" keymap="$default"/>
        </action>

        <action id="Filin.Chat.ToggleChatToolWindow"
                class="com.tabbyml.intellijtabby.actions.chat.ToggleChatToolWindow"
                text="Switch Focus Between Editor and Chat"
                description="Switch focus between editor and chat, add selected text to chat if any.">
            <keyboard-shortcut first-keystroke="ctrl L" keymap="$default"/>
        </action>

        <action id="Filin.GenerateCommitMessage"
                class="com.tabbyml.intellijtabby.actions.GenerateCommitMessage"
                text="Generate Commit Message"
                description="Generate a commit message based on the changes in the current Git repository.">
        </action>

        <action id="Filin.InlineChat.Resolve.Accept"
                class="com.tabbyml.intellijtabby.inlineChat.InlineChatAcceptAction"
                text="Accept Inline Edit"
                description="Accept the inline edit suggestion.">
            <keyboard-shortcut first-keystroke="ctrl shift D" keymap="$default"/>
        </action>

        <action id="Filin.InlineChat.Resolve.Discard"
                class="com.tabbyml.intellijtabby.inlineChat.InlineChatDiscardAction"
                text="Discard Inline Edit"
                description="Discard the inline edit suggestion.">
            <keyboard-shortcut first-keystroke="ctrl ESCAPE" keymap="$default"/>
        </action>
    </actions>
</idea-plugin>